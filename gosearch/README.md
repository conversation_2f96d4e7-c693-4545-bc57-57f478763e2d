<p align='center'>
<img src='img/gosearch-logo.png' height=50% width=50%><br>
<i>This project heavily relies on contributors, please see <a href="#contributing">Contributing</a> for more details.</i><br>
<code>go install github.com/ibnaleem/gosearch@latest</code>
</p>

<p align="center">
  <img src="https://github.com/ibnaleem/gosearch/actions/workflows/go.yml/badge.svg?event=push" alt="GitHub Actions Badge"> <img src="https://img.shields.io/github/last-commit/ibnaleem/gosearch"> <img src="https://img.shields.io/github/commit-activity/w/ibnaleem/gosearch"> <img src="https://img.shields.io/github/contributors/ibnaleem/gosearch"> <img alt="Number of websites" src="https://img.shields.io/badge/websites-305-blue"> <img alt="GitHub repo size" src="https://img.shields.io/github/repo-size/ibnaleem/gosearch"> <img alt="GitHub License" src="https://img.shields.io/github/license/ibnaleem/gosearch">
</p>
<hr>

## Overview
<p align='center'>
<img src='img/1.png' height=80% width=80%><br>
<img src='img/2.png' height=80% width=80%><br>
<img src='img/3.png' height=80% width=80%><br>
<img src='img/4.png' height=80% width=80%><br>
</p>

You don't have time searching every profile with a username. Instead, you can leverage concurrency and a binary that does the work for you, and then some.

I initially wrote this project to learn Go, an upcoming programming language used for backend services. I decided to create a Sherlock clone, addressing some of its faults, limitations, and adding more features. This eventually led to a community driven OSINT tool that was [praised in the OSINT letter.](https://osintnewsletter.com/p/62)

GoSearch isn't limited to searching websites; it can search **900k leaked credentials** from [HudsonRock's Cybercrime Intelligence API](https://cavalier.hudsonrock.com/api/json/v2/osint-tools/search-by-username?username=mrrobot), over **3.2 billion leaked credentials** from [ProxyNova's Combination Of Many Breaches API](https://www.proxynova.com/tools/comb/), and **18 billion leaked credentials** from BreachDirectory.org with an API key (see [Use Cases](#use-cases))

## Installation
> [!WARNING]  
> If you are on 32-bit architecture, please [use this branch](https://github.com/ibnaleem/gosearch/tree/32-bit) or GoSearch will fail to build. For an in-depth overview of this issue, please see [#72](https://github.com/ibnaleem/gosearch/issues/72)

> [!WARNING]  
> If you're using Windows Defender, it might mistakenly flag GoSearch as malware. Rest assured, GoSearch is not malicious; you can review the full source code yourself to verify this. For an in-depth overview of this issue, please see [#90](https://github.com/ibnaleem/gosearch/issues/90)
```
$ go install github.com/ibnaleem/gosearch@latest
```
### Unix:
```
$ gosearch -u [username]
```
### Windows
```
C:\Users\<USER>